<?php

namespace App\Admin\Actions\Grid\Dispute;

use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Grid\LazyRenderable;
use App\Models\Order as OrderModel;
use App\Models\Merchant;
use App\Models\MerchantUrl;
use App\Models\Channel;
use App\Admin\Repositories\Order;

class ApplyOrder extends LazyRenderable
{
    public function grid(): Grid
    {
        return Grid::make(new Order(['paymentOrder', 'card']), function (Grid $grid) {
            if (count(request()->toArray()) <= 2) {
                request()->offsetSet('app-admin-actions-grid-dispute-applyorder_completed_at', [
                    'start' => date('Y-m-d 00:00:00', strtotime('-3 month')),
                    'end'   => date('Y-m-d H:i:s')
                ]);
            }

			$_this = $this;

            $asField = ['*', 'order_id as _order_id', 'type as order_type', 'status as order_status'];
			$grid->model()->select($asField)->leftJoin('chargeback', 'chargeback.order_id', '=', 'orders.order_id')->whereNull('chargeback.order_id');
			$grid->model()->whereIn('orders.type', [OrderModel::TYPES_SALE, OrderModel::TYPES_CAPTURE]);
			$grid->model()->whereIn('orders.status', [OrderModel::STATUS_APPROVED, OrderModel::STATUS_RECEIVED]);
			$grid->model()->orderBy('orders.order_id', 'desc');

			$grid->content('选项')->display(function() use($_this) {
				return $_this->build($this->_order_id);
			});

			$grid->column('_order_id', '订单号');
			$grid->column('order_type', '交易类型')->display(function ($value) {
				return OrderModel::$typesMap[$value] ?? '未知';
			});
			$grid->column('order_status', '支付状态')->display(function ($value) {
				return OrderModel::$statusMap[$value] ?? '未知';
			})->dot(['0' => 'danger', '1' => 'success', '2' => 'primary', '3' => 'primary', '4' => 'danger']);
			$grid->column('result', '返回结果');
			$grid->column('remark', '备注');
			$grid->column('parent_order_id', '原始订单号');
			$grid->column('completed_at', '完成时间')->sortable();

			$grid->quickSearch(function($model, $query){
				$model->where('orders.order_id',$query);
			});
			$grid->enableDialogCreate();
			$grid->disableRefreshButton();
			$grid->disableRowSelector();
			$grid->disableCreateButton();
			$grid->disableEditButton();
			$grid->showFilter();
			$grid->showFilterButton();
			$grid->disableDeleteButton();
			$grid->disableViewButton();
			$grid->disableActions();

			$grid->filter(function (Grid\Filter $filter) {
				$filter->equal('paymentOrder.order_id', '订单号')->width(4);
				$filter->equal('paymentOrder.payment_order_id', '渠道订单号')->width(4);
				$filter->equal('order_number', '商户订单号')->width(4);
                $filter->equal('code', '返回代码')->width(4);
				$filter->equal('merchant_id', 'MID')->select(
                    Merchant::where('status', Merchant::STATUS_ENABLE)
                        ->pluck('merchant_name', 'merchant_id')
                        ->map(static function ($item, $key) {
                            return $item . ':' . $key;
                        })
                        ->toArray()
                )->width(4);
				$filter->equal('url_id', '网站ID')->select(MerchantUrl::get()->pluck('url_name', 'id')->toArray())->width(4);
				$filter->equal('type', '交易类型')->select(OrderModel::$typesMap)->width(4);
				$filter->equal('is_3d', '是否3D')->select(['否', '是'])->width(4);
				$filter->in('channel_id', '账单标识ID')->multipleSelect(Channel::all()->pluck('channel', 'id')->toArray())->width(4);
				$filter->whereBetween('created_at', function ($query) {
                    $start = $this->input['start'] ?? '';
                    $end   = $this->input['end'] ?? '';

                    if (!empty($end)) {
                        $query->where('orders.created_at', '<=', $end);
                    }

                    if (!empty($start)) {
                        $query->where('orders.created_at', '>=', $start);
                    }
                })->datetime(['sideBySide'=>true])->width(8);
				$filter->between('completed_at', '完成时间')->datetime(['sideBySide'=>true])->width(8);
			});
        });
    }

    /**
     * show form button by dialog
     *
     * @param [int] $orderId
     * @return string
     */
	protected function build($orderId)
	{
		Form::dialog('<b>拒付工单</b>')
		    ->click('.chargeback-apply-forms')
		    ->width('70%')
            ->height('95%')
		    ->success('Dcat.reload()');
		$url = '/admin/dcat-api/render?_current_=/admin/chargeback_order?&order_id='. $orderId .'&renderable=App_Admin_Actions_Grid_Dispute_ApplyChargeback';
		return "<span class='btn btn-info chargeback-apply-forms' data-url='{$url}'>拒付</span>";
    }
}
